<template>
  <div class="divBox">
    <el-card
      :bordered="false"
      shadow="never"
      class="ivu-mt"
      :body-style="{ padding: 0 }"
    >
      <div class="padding-add">
        <el-form size="small" inline label-position="right" @submit.native.prevent>
          <el-form-item label="时间选择：">
            <el-date-picker
              v-model="timeVal"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              size="small"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placement="bottom-end"
              @change="onchangeTime"
              class="selWidth"
            />
          </el-form-item>
          <el-form-item label="操作步骤：">
            <el-select
              v-model="tableFrom.handleType"
              placeholder="请选择操作步骤"
              size="small"
              clearable
              class="selWidth"
            >
              <el-option label="发短信" :value="1" />
              <el-option label="下单" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearchList">查询</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="box-card mt14" :body-style="{ padding: '0 20px 20px' }" shadow="never" :bordered="false">
      <div class="mt5">
        <el-button size="small" @click="exports">导出Excel</el-button>
      </div>
      
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
        highlight-current-row
        class="mt20"
      >
        <el-table-column prop="productCode" label="商品编号" min-width="150" />
        <el-table-column prop="productName" label="商品名称" min-width="200" />
        <el-table-column prop="operationStep" label="操作步骤" min-width="150" align="center" />
        <el-table-column prop="result" label="处理结果" min-width="150" align="center" />
        <el-table-column prop="totalCount" label="总次数" min-width="120" align="center">
          <template slot-scope="scope">
            <span class="total-count">{{ scope.row.totalCount }}</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="block">
        <el-pagination
          background
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { failureReasonListApi, failureReasonExcelApi } from '@/api/order';

export default {
  name: 'FailureReason',
  data() {
    return {
      tableData: {
        data: [],
        total: 0,
      },
      listLoading: true,
      tableFrom: {
        page: 1,
        limit: 20,
        startTime: '',
        endTime: '',
        handleType: '',
      },
      timeVal: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 重置表单
    handleReset() {
      this.tableFrom.startTime = '';
      this.tableFrom.endTime = '';
      this.tableFrom.handleType = '';
      this.tableFrom.page = 1;
      this.timeVal = [];
      this.handleSearchList();
    },
    // 搜索列表
    handleSearchList() {
      this.tableFrom.page = 1;
      this.getList();
    },
    // 时间选择
    onchangeTime(e) {
      this.timeVal = e;
      if (e && e.length === 2) {
        this.tableFrom.startTime = e[0];
        this.tableFrom.endTime = e[1];
      } else {
        this.tableFrom.startTime = '';
        this.tableFrom.endTime = '';
      }
    },
    // 获取列表数据
    getList() {
      this.listLoading = true;
      failureReasonListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list || [];
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    // 分页改变
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    // 每页数量改变
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 导出Excel
    exports() {
      let data = {
        startTime: this.tableFrom.startTime,
        endTime: this.tableFrom.endTime,
        handleType: this.tableFrom.handleType,
      };
      failureReasonExcelApi(data).then((res) => {
        if (res.fileName) {
          window.open(res.fileName);
        } else {
          this.$modal.msgSuccess('导出成功');
        }
      }).catch(() => {
        this.$modal.msgError('导出失败');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.selWidth {
  width: 300px;
}

.total-count {
  font-weight: bold;
  color: #e6a23c;
}

.mt5 {
  margin-top: 5px;
}

.mt14 {
  margin-top: 14px;
}

.mt20 {
  margin-top: 20px;
}

.padding-add {
  padding: 20px;
}

.block {
  margin-top: 20px;
  text-align: right;
}
</style>
