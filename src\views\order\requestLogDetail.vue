<template>
  <div class="divBox">
    <el-card
      :bordered="false"
      shadow="never"
      class="ivu-mt"
      :body-style="{ padding: 0 }"
    >
      <div class="padding-add">
        <el-form size="small" inline label-position="right" @submit.native.prevent>
          <el-form-item label="时间选择：">
            <el-date-picker
              v-model="timeVal"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              size="small"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placement="bottom-end"
              @change="onchangeTime"
              class="selWidth"
            />
          </el-form-item>
          <el-form-item label="渠道：">
            <el-select
              v-model="tableFrom.channelId"
              placeholder="请选择渠道"
              size="small"
              clearable
              class="selWidth"
            >
              <el-option
                v-for="(label, value) in channelOptions"
                :key="value"
                :label="label"
                :value="parseInt(value)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="操作步骤：">
            <el-select
              v-model="tableFrom.handleType"
              placeholder="请选择操作步骤"
              size="small"
              clearable
              class="selWidth"
            >
              <el-option label="发短信" :value="1" />
              <el-option label="下单" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码：">
            <el-input
              v-model="tableFrom.phone"
              placeholder="请输入手机号码"
              size="small"
              clearable
              class="selWidth"
            />
          </el-form-item>
          <el-form-item label="平台订单号：">
            <el-input
              v-model="tableFrom.platOrderNo"
              placeholder="请输入平台订单号"
              size="small"
              clearable
              class="selWidth"
            />
          </el-form-item>
          <el-form-item label="产品编码：">
            <el-input
              v-model="tableFrom.productCode"
              placeholder="请输入产品编码"
              size="small"
              clearable
              class="selWidth"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearchList">查询</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="box-card mt14" :body-style="{ padding: '0 20px 20px' }" shadow="never" :bordered="false">
      <div class="mt5">
        <el-button size="small" @click="exports">导出Excel</el-button>
      </div>
      
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        size="small"
        highlight-current-row
        class="mt20"
      >
        <el-table-column prop="logId" label="日志ID" min-width="100" align="center" />
        <el-table-column prop="channelId" label="渠道" min-width="120" align="center" />
        <el-table-column prop="phone" label="手机号码" min-width="130" />
        <el-table-column prop="platOrderNo" label="平台订单号" min-width="180" />
        <el-table-column prop="productCode" label="产品编码" min-width="150" />
        <el-table-column prop="productName" label="产品名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="handleType" label="操作步骤" min-width="120" align="center" />
        <el-table-column prop="appName" label="应用名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="ip" label="IP地址" min-width="130" />
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" min-width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="block">
        <el-pagination
          background
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      title="请求日志详情"
      :visible.sync="detailVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="detailData" class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">日志ID：</span>
              <span class="value">{{ detailData.logId }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">渠道：</span>
              <span class="value">{{ detailData.channelId }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">手机号码：</span>
              <span class="value">{{ detailData.phone }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">平台订单号：</span>
              <span class="value">{{ detailData.platOrderNo }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">产品编码：</span>
              <span class="value">{{ detailData.productCode }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">产品名称：</span>
              <span class="value">{{ detailData.productName }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">操作步骤：</span>
              <span class="value">{{ detailData.handleType }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">应用名称：</span>
              <span class="value">{{ detailData.appName }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">应用包名：</span>
              <span class="value">{{ detailData.appPackage }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">IP地址：</span>
              <span class="value">{{ detailData.ip }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">一级触点：</span>
              <span class="value">{{ detailData.firstContact }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">二级触点：</span>
              <span class="value">{{ detailData.secContact }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">短信验证码：</span>
              <span class="value">{{ detailData.smsCode }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">订单号：</span>
              <span class="value">{{ detailData.orderNo }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="label">页面URL：</span>
              <span class="value">{{ detailData.pageUrl }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="label">User Agent：</span>
              <span class="value">{{ detailData.ua }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="label">入参：</span>
              <div class="value code-block">{{ detailData.params }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <span class="label">结果：</span>
              <div class="value code-block">{{ detailData.result }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ detailData.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ detailData.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { requestLogDetailListApi, requestLogDetailExcelApi } from '@/api/order';

export default {
  name: 'RequestLogDetail',
  data() {
    return {
      tableData: {
        data: [],
        total: 0,
      },
      listLoading: true,
      tableFrom: {
        page: 1,
        limit: 20,
        startTime: '',
        endTime: '',
        channelId: '',
        handleType: '',
        phone: '',
        platOrderNo: '',
        productCode: '',
      },
      timeVal: [],
      channelOptions: {
        1: '九品一未',
        2: '极有汇',
        3: '深圳硕软',
        4: '银象网络',
        5: '广州华兆',
        6: '广州学诚',
        7: '北京云霄',
        8: '千端',
      },
      detailVisible: false,
      detailData: null,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 重置表单
    handleReset() {
      this.tableFrom.startTime = '';
      this.tableFrom.endTime = '';
      this.tableFrom.channelId = '';
      this.tableFrom.handleType = '';
      this.tableFrom.phone = '';
      this.tableFrom.platOrderNo = '';
      this.tableFrom.productCode = '';
      this.tableFrom.page = 1;
      this.timeVal = [];
      this.handleSearchList();
    },
    // 搜索列表
    handleSearchList() {
      this.tableFrom.page = 1;
      this.getList();
    },
    // 时间选择
    onchangeTime(e) {
      this.timeVal = e;
      if (e && e.length === 2) {
        this.tableFrom.startTime = e[0];
        this.tableFrom.endTime = e[1];
      } else {
        this.tableFrom.startTime = '';
        this.tableFrom.endTime = '';
      }
    },
    // 获取列表数据
    getList() {
      this.listLoading = true;
      requestLogDetailListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list || [];
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    // 分页改变
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    // 每页数量改变
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 显示详情
    showDetail(row) {
      this.detailData = row;
      this.detailVisible = true;
    },
    // 导出Excel
    exports() {
      let data = {
        startTime: this.tableFrom.startTime,
        endTime: this.tableFrom.endTime,
        channelId: this.tableFrom.channelId,
        handleType: this.tableFrom.handleType,
        phone: this.tableFrom.phone,
        platOrderNo: this.tableFrom.platOrderNo,
        productCode: this.tableFrom.productCode,
      };
      requestLogDetailExcelApi(data).then((res) => {
        if (res.fileName) {
          window.open(res.fileName);
        } else {
          this.$modal.msgSuccess('导出成功');
        }
      }).catch(() => {
        this.$modal.msgError('导出失败');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.selWidth {
  width: 300px;
}

.mt5 {
  margin-top: 5px;
}

.mt14 {
  margin-top: 14px;
}

.mt20 {
  margin-top: 20px;
}

.padding-add {
  padding: 20px;
}

.block {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  .detail-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    
    .label {
      font-weight: bold;
      color: #606266;
      min-width: 120px;
      flex-shrink: 0;
    }
    
    .value {
      color: #303133;
      word-break: break-all;
      flex: 1;
    }
    
    .code-block {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
</style>
